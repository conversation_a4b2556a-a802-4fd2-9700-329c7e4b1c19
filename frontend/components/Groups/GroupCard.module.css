.groupCard {
  background-color: var(--white);
  border-radius: 12px;
  box-shadow: var(--shadow);
  overflow: hidden;
}

.groupHeader {
  height: 80px;
  background: linear-gradient(135deg, var(--primary-navy), var(--secondary-navy));
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
}

.groupInfo {
  padding: 15px;
  text-align: center;
}

.groupName {
  font-size: 15px;
  font-weight: 600;
  color: var(--primary-navy);
  margin-bottom: 6px;
}

.groupMeta {
  font-size: 12px;
  color: var(--text-light);
  margin-bottom: 12px;
}

.groupActions {
  display: flex;
  gap: 8px;
  justify-content: center;
}
