/* Modal Overlay */
.modalOverlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: 20px;
}

/* Modal Content */
.modalContent {
  background: white;
  border-radius: 12px;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
  width: 100%;
  max-width: 500px;
  max-height: 90vh;
  overflow-y: auto;
  animation: modalSlideIn 0.3s ease-out;
}

@keyframes modalSlideIn {
  from {
    opacity: 0;
    transform: translateY(-20px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

/* Modal Header */
.modalHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24px 24px 0 24px;
  border-bottom: 1px solid #e5e7eb;
  margin-bottom: 24px;
}

.modalHeader h2 {
  margin: 0;
  font-size: 1.5rem;
  font-weight: 600;
  color: #1f2937;
}

.closeButton {
  background: none;
  border: none;
  font-size: 1.25rem;
  color: #6b7280;
  cursor: pointer;
  padding: 8px;
  border-radius: 6px;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 36px;
  height: 36px;
}

.closeButton:hover {
  background-color: #f3f4f6;
  color: #374151;
}

.closeButton:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* Form Styles */
.form {
  padding: 0 24px 24px 24px;
}

.formGroup {
  margin-bottom: 20px;
}

.label {
  display: block;
  margin-bottom: 6px;
  font-weight: 500;
  color: #374151;
  font-size: 0.875rem;
}

.input,
.textarea {
  width: 100%;
  padding: 12px 16px;
  border: 1px solid #d1d5db;
  border-radius: 8px;
  font-size: 0.875rem;
  transition: all 0.2s ease;
  font-family: inherit;
  background-color: white;
}

.input:focus,
.textarea:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.input:disabled,
.textarea:disabled {
  background-color: #f9fafb;
  color: #6b7280;
  cursor: not-allowed;
}

.inputError {
  border-color: #ef4444;
}

.inputError:focus {
  border-color: #ef4444;
  box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1);
}

.textarea {
  resize: vertical;
  min-height: 100px;
}

/* Character Count */
.charCount {
  text-align: right;
  font-size: 0.75rem;
  color: #6b7280;
  margin-top: 4px;
}

/* Error Messages */
.errorMessage {
  display: block;
  color: #ef4444;
  font-size: 0.75rem;
  margin-top: 4px;
}

.generalError {
  background-color: #fef2f2;
  border: 1px solid #fecaca;
  color: #dc2626;
  padding: 12px 16px;
  border-radius: 8px;
  font-size: 0.875rem;
  margin-bottom: 20px;
}

/* Form Actions */
.formActions {
  display: flex;
  gap: 12px;
  justify-content: flex-end;
  margin-top: 32px;
  padding-top: 20px;
  border-top: 1px solid #e5e7eb;
}

.formActions button {
  min-width: 100px;
}

/* Responsive Design */
@media (max-width: 640px) {
  .modalOverlay {
    padding: 10px;
  }
  
  .modalContent {
    max-height: 95vh;
  }
  
  .modalHeader {
    padding: 20px 20px 0 20px;
    margin-bottom: 20px;
  }
  
  .modalHeader h2 {
    font-size: 1.25rem;
  }
  
  .form {
    padding: 0 20px 20px 20px;
  }
  
  .formActions {
    flex-direction: column-reverse;
  }
  
  .formActions button {
    width: 100%;
  }
}
