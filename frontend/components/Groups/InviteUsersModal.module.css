/* Modal Overlay */
.modalOverlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: 20px;
}

/* Modal Content */
.modalContent {
  background: white;
  border-radius: 12px;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
  width: 100%;
  max-width: 600px;
  max-height: 90vh;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  animation: modalSlideIn 0.3s ease-out;
}

@keyframes modalSlideIn {
  from {
    opacity: 0;
    transform: translateY(-20px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

/* Modal Header */
.modalHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24px 24px 0 24px;
  border-bottom: 1px solid #e5e7eb;
  margin-bottom: 0;
}

.modalHeader h2 {
  margin: 0;
  font-size: 1.25rem;
  font-weight: 600;
  color: #1f2937;
}

.closeButton {
  background: none;
  border: none;
  font-size: 1.25rem;
  color: #6b7280;
  cursor: pointer;
  padding: 8px;
  border-radius: 6px;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 36px;
  height: 36px;
}

.closeButton:hover {
  background-color: #f3f4f6;
  color: #374151;
}

.closeButton:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* Modal Body */
.modalBody {
  flex: 1;
  overflow-y: auto;
  padding: 24px;
  display: flex;
  flex-direction: column;
  gap: 20px;
}

/* Search Section */
.searchSection {
  position: sticky;
  top: 0;
  background: white;
  z-index: 10;
  padding-bottom: 4px;
}

.searchInputContainer {
  position: relative;
  display: flex;
  align-items: center;
}

.searchInputContainer > i:first-child {
  position: absolute;
  left: 12px;
  color: #6b7280;
  z-index: 1;
}

.searchInput {
  width: 100%;
  padding: 12px 16px 12px 40px;
  border: 1px solid #d1d5db;
  border-radius: 8px;
  font-size: 0.875rem;
  transition: all 0.2s ease;
  background-color: white;
}

.searchInput:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.searchInput:disabled {
  background-color: #f9fafb;
  color: #6b7280;
  cursor: not-allowed;
}

.searchInputContainer > i:last-child {
  position: absolute;
  right: 12px;
  color: #3b82f6;
}

/* Selected Section */
.selectedSection {
  background-color: #f8fafc;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  padding: 16px;
}

.selectedSection h3 {
  margin: 0 0 12px 0;
  font-size: 0.875rem;
  font-weight: 600;
  color: #374151;
}

.selectedUsers {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.selectedUser {
  display: flex;
  align-items: center;
  gap: 8px;
  background: white;
  border: 1px solid #d1d5db;
  border-radius: 20px;
  padding: 4px 8px 4px 4px;
  font-size: 0.75rem;
}

.removeButton {
  background: none;
  border: none;
  color: #6b7280;
  cursor: pointer;
  padding: 2px;
  border-radius: 50%;
  width: 16px;
  height: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.625rem;
  transition: all 0.2s ease;
}

.removeButton:hover {
  background-color: #fee2e2;
  color: #dc2626;
}

/* Results Section */
.resultsSection {
  flex: 1;
  min-height: 200px;
}

.resultsSection h3 {
  margin: 0 0 16px 0;
  font-size: 0.875rem;
  font-weight: 600;
  color: #374151;
}

/* User Avatar */
.userAvatar {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  overflow: hidden;
  flex-shrink: 0;
}

.userAvatar img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.avatarPlaceholder {
  width: 100%;
  height: 100%;
  background-color: #f3f4f6;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  color: #6b7280;
  font-size: 0.75rem;
}

.userName {
  font-size: 0.75rem;
  font-weight: 500;
  color: #374151;
}

/* Search Results */
.usersList {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.userItem {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.userItem:hover {
  border-color: #d1d5db;
  background-color: #f9fafb;
}

.userItem.selected {
  border-color: #3b82f6;
  background-color: #eff6ff;
}

.userInfo {
  flex: 1;
}

.userInfo h4 {
  margin: 0 0 2px 0;
  font-size: 0.875rem;
  font-weight: 600;
  color: #374151;
}

.userNickname {
  margin: 0 0 2px 0;
  font-size: 0.75rem;
  color: #6b7280;
}

.userEmail {
  margin: 0;
  font-size: 0.75rem;
  color: #9ca3af;
}

.selectionIndicator {
  color: #3b82f6;
  font-size: 1.25rem;
}

.userItem:not(.selected) .selectionIndicator {
  color: #d1d5db;
}

/* Empty States */
.noResults,
.searchPrompt {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 20px;
  text-align: center;
  color: #6b7280;
  gap: 8px;
}

.noResults i,
.searchPrompt i {
  font-size: 2rem;
  color: #d1d5db;
  margin-bottom: 8px;
}

.noResults p,
.searchPrompt p {
  margin: 0;
  font-size: 0.875rem;
  font-weight: 500;
}

.noResults small,
.searchPrompt small {
  font-size: 0.75rem;
  color: #9ca3af;
}

/* Error Message */
.errorMessage {
  background-color: #fef2f2;
  border: 1px solid #fecaca;
  color: #dc2626;
  padding: 12px 16px;
  border-radius: 8px;
  font-size: 0.875rem;
  display: flex;
  align-items: center;
  gap: 8px;
}

/* Modal Footer */
.modalFooter {
  display: flex;
  gap: 12px;
  justify-content: flex-end;
  padding: 20px 24px 24px;
  border-top: 1px solid #e5e7eb;
  background-color: #f9fafb;
}

.modalFooter button {
  display: flex;
  align-items: center;
  gap: 8px;
  min-width: 120px;
  justify-content: center;
}

/* Responsive Design */
@media (max-width: 640px) {
  .modalOverlay {
    padding: 10px;
  }

  .modalContent {
    max-height: 95vh;
  }

  .modalHeader {
    padding: 20px 20px 0 20px;
  }

  .modalHeader h2 {
    font-size: 1.125rem;
  }

  .modalBody {
    padding: 20px;
  }

  .modalFooter {
    flex-direction: column-reverse;
    padding: 16px 20px 20px;
  }

  .modalFooter button {
    width: 100%;
  }

  .selectedUsers {
    gap: 6px;
  }

  .selectedUser {
    font-size: 0.6875rem;
  }
}
